import { create, index, remove, update } from '@/services/vehicles';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

type EditModalProps = {
  /** 设置标记点 */
  setMarkers: React.Dispatch<React.SetStateAction<Marker[]>>;
  /** 设置激活的标记点 */
  setActiveMarkers: (id: number) => void;
  /** 字典数据 */
  dictionarie: any;
};

const EditTable: React.FC<EditModalProps> = ({
  setMarkers,
  setActiveMarkers,
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Vehicle | undefined>(undefined);

  console.log('=------', dictionarie);
  // 获取车辆类型选项
  const vehicleTypeList =
    dictionarie?.list?.filter(
      (item: API.Dictionarie) => item.type === '车辆类型' && item.status === 1,
    ) || [];
  const vehicleTypeValueEnum: { [key: string]: string } = {};
  vehicleTypeList.forEach((item: API.Dictionarie) => {
    vehicleTypeValueEnum[item.code] = item.name;
  });

  const handleSave = async (values: API.Vehicle) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Vehicle) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Vehicle, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '车牌号',
      dataIndex: 'plateNumber',
      key: 'plateNumber',
    },
    {
      title: '车辆类型',
      dataIndex: 'vehicleType',
      key: 'vehicleType',
      valueEnum: vehicleTypeValueEnum,
      render: (_, record) =>
        vehicleTypeValueEnum[record.vehicleType || ''] ||
        record.vehicleType ||
        '-',
    },
    {
      title: '车辆状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '员工',
      dataIndex: ['employee', 'name'],
      key: 'employee',
    },
    {
      title: '手机号',
      dataIndex: ['employee', 'phone'],
      key: 'phone',
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Vehicle>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          setMarkers(
            (data?.list || [])
              .filter((item) => !!item.longitude && !!item.latitude)
              .map((item) => ({
                position: [item.longitude!, item.latitude!],
                title: item.plateNumber,
                id: item.id,
                icon: {
                  image: '/logo.png',
                  size: [36, 36],
                  imageSize: [36, 36],
                  // offset?: [number, number]; // 图标偏移量 [x, y]
                },
              })),
          );
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        onRow={(record) => ({
          onClick: () => {
            setActiveMarkers(record.id);
          },
        })}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(EditTable);
