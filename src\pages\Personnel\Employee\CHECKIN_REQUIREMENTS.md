# 员工打卡要求功能说明

## 功能概述

为了确保员工定期进行出车打卡，系统新增了以下功能：

1. **员工管理列表显示最后打卡时间**
2. **订单分配时检查员工打卡状态**

## 功能详情

### 1. 员工管理列表增强

在员工管理页面新增"最后打卡时间"列，显示员工的打卡状态：

- **绿色标签**：3天内打卡，显示"今天"或"X天前"
- **橙色标签**：3-7天前打卡，显示"X天前"
- **红色标签**：超过7天未打卡，显示"超期X天"
- **红色标签**：从未打卡，显示"从未打卡"

### 2. 订单分配限制

在派单时会检查员工的打卡状态：

#### 员工卡片视图
- 显示员工的打卡状态标签
- 超过7天未打卡或从未打卡的员工会显示红色警告
- 不可分配的员工会显示"不能分配订单，需要先进行出车打卡"

#### 详细列表视图
- 新增"打卡状态"列
- 显示打卡状态和是否可派单
- 不可派单的员工会显示"不可派单"提示

#### 派单按钮
- 选择超过7天未打卡的员工时，"确定派单"按钮会被禁用
- 确保只有符合打卡要求的员工才能被分配订单

### 3. 推荐度评分调整

员工推荐度评分现在会考虑打卡状态：

- **从未打卡或超过7天未打卡**：推荐度直接设为0
- **3-7天未打卡**：推荐度扣1分
- **3天内打卡**：不影响推荐度

## 业务规则

### 打卡要求
- 员工每周至少需要进行一次出车打卡
- 超过7天未打卡的员工不能被分配新订单
- 从未打卡的员工不能被分配订单

### 状态分类
1. **正常**（绿色）：3天内有打卡记录
2. **警告**（橙色）：3-7天前有打卡记录
3. **超期**（红色）：超过7天未打卡
4. **从未打卡**（红色）：没有任何打卡记录

## 技术实现

### 数据结构更新
```typescript
interface Employee {
  // ... 其他字段
  lastCheckinTime?: Date; // 最后打卡时间
}
```

### API接口
- `GET /employees/:id/last-checkin` - 获取员工最后打卡时间

### 核心函数
- `checkCheckinStatus(employee)` - 检查员工打卡状态
- `getRecommendationScore(employee)` - 计算推荐度（包含打卡因素）

## 使用说明

### 管理员操作
1. 在员工管理页面查看所有员工的打卡状态
2. 重点关注红色标签的员工，提醒其进行打卡
3. 在派单时系统会自动过滤不符合要求的员工

### 员工要求
1. 每周至少进行一次出车打卡
2. 打卡时需要上传分组照片（车辆外观、服务人员、车内情况）
3. 保持定期打卡以确保能够接收订单分配

## 注意事项

1. 该功能确保服务质量和员工管理规范
2. 超期员工需要先完成打卡才能重新接单
3. 系统会实时检查员工打卡状态
4. 管理员可以通过员工管理页面监控所有员工的打卡情况
