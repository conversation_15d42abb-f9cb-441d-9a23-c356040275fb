import { OrderStatus } from '@/constant';
import { index } from '@/services/order';
import {
  ClockCircleOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  PhoneOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  message,
  Row,
  Space,
  Spin,
  Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';

interface StatusBoardViewProps {
  onViewDetail: (order: API.Order) => void;
  onViewLog: (order: API.Order) => void;
  onAssign: (order: API.Order) => void;
  onStart: (order: API.Order, employeeId: number) => void;
  onComplete: (order: API.Order, employeeId: number) => void;
  onViewReview: (order: API.Order) => void;
  onAuditRefund: (order: API.Order) => void;
  onViewServicePhotos: (order: API.Order) => void;
  onViewSpecialNotes: (order: API.Order) => void;
}

const StatusBoardView: React.FC<StatusBoardViewProps> = ({
  onViewDetail,
  onViewLog,
  onAssign,
  onStart,
  onComplete,
  onViewReview,
  onAuditRefund,
  onViewServicePhotos,
  onViewSpecialNotes,
}) => {
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<API.Order[]>([]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await index({});
      if (errCode) {
        message.error(msg || '获取订单列表失败');
      } else {
        const orderList = data?.list || [];
        setOrders(orderList);
      }
    } catch (error) {
      console.error('获取订单失败:', error);
      message.error('获取订单失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // 按状态分组订单
  const groupedOrders = orders.reduce((acc, order) => {
    const status = order.status;
    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(order);
    return acc;
  }, {} as Record<string, API.Order[]>);

  // 状态配置
  const statusConfig = {
    [OrderStatus.待付款]: { color: 'orange', title: '待付款' },
    [OrderStatus.待接单]: { color: 'blue', title: '待接单' },
    [OrderStatus.待服务]: { color: 'cyan', title: '待服务' },
    [OrderStatus.已出发]: { color: 'geekblue', title: '已出发' },
    [OrderStatus.服务中]: { color: 'purple', title: '服务中' },
    [OrderStatus.已完成]: { color: 'green', title: '已完成' },
    [OrderStatus.已评价]: { color: 'lime', title: '已评价' },
    [OrderStatus.已取消]: { color: 'red', title: '已取消' },
    [OrderStatus.退款中]: { color: 'volcano', title: '退款中' },
    [OrderStatus.已退款]: { color: 'magenta', title: '已退款' },
  };

  // 显式定义类型断言
  const statusConfigTyped = statusConfig as Record<
    OrderStatus,
    { color: string; title: string }
  >;

  const renderOrderCard = (order: API.Order) => {
    const serviceName =
      order.orderDetails?.[0]?.service?.serviceName || '未知服务';
    const serviceType =
      order.orderDetails?.[0]?.service?.serviceType?.name || '未知类型';

    return (
      <Card
        key={order.id}
        size="small"
        style={{ marginBottom: '8px', cursor: 'pointer' }}
        hoverable
        onClick={() => onViewDetail(order)}
      >
        <div style={{ marginBottom: '8px' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span style={{ fontWeight: 'bold', fontSize: '14px' }}>
              {order.sn}
            </span>
            <Tag color={statusConfigTyped[order.status as OrderStatus]?.color}>
              {statusConfigTyped[order.status as OrderStatus]?.title}
            </Tag>
          </div>
        </div>

        <div style={{ marginBottom: '8px' }}>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <div>{serviceName}</div>
            <div>{serviceType}</div>
          </div>
        </div>

        <div style={{ marginBottom: '8px' }}>
          <Space size="small">
            <UserOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '12px' }}>
              {order.customer?.nickname || '未知客户'}
            </span>
          </Space>
        </div>

        <div style={{ marginBottom: '8px' }}>
          <Space size="small">
            <PhoneOutlined style={{ color: '#52c41a' }} />
            <span style={{ fontSize: '12px' }}>
              {order.customer?.phone || '未知电话'}
            </span>
          </Space>
        </div>

        {order.serviceTime && (
          <div style={{ marginBottom: '8px' }}>
            <Space size="small">
              <ClockCircleOutlined style={{ color: '#722ed1' }} />
              <span style={{ fontSize: '12px' }}>
                {new Date(order.serviceTime).toLocaleString()}
              </span>
            </Space>
          </div>
        )}

        <div style={{ marginBottom: '8px' }}>
          <Space size="small">
            <DollarOutlined style={{ color: '#fa8c16' }} />
            <span style={{ fontSize: '12px' }}>¥{order.totalFee}</span>
            {order.hasAdditionalServices && (
              <Tag color="blue">+¥{order.additionalServiceAmount}</Tag>
            )}
            {order.hasSpecialNote && (
              <Tag color="orange" icon={<ExclamationCircleOutlined />}>
                特殊情况
              </Tag>
            )}
          </Space>
        </div>

        {order.orderDetails?.[0]?.userRemark && (
          <div style={{ marginBottom: '8px' }}>
            <div
              style={{ fontSize: '12px', color: '#666', fontStyle: 'italic' }}
            >
              用户备注：{order.orderDetails[0].userRemark}
            </div>
          </div>
        )}

        {order.employee && (
          <div style={{ marginBottom: '8px' }}>
            <Space size="small">
              <Avatar size="small" icon={<UserOutlined />} />
              <span style={{ fontSize: '12px' }}>{order.employee.name}</span>
            </Space>
          </div>
        )}

        <div style={{ marginTop: '8px' }}>
          <Space size="small" wrap>
            <Button
              size="small"
              type="link"
              onClick={(e) => {
                e.stopPropagation();
                onViewLog(order);
              }}
            >
              日志
            </Button>

            {order.status === OrderStatus.待接单 && (
              <Button
                size="small"
                type="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  onAssign(order);
                }}
              >
                派单
              </Button>
            )}

            {[OrderStatus.待服务, OrderStatus.已出发].includes(
              order.status as any,
            ) && (
              <>
                <Button
                  size="small"
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    onStart(order, order.employeeId!);
                  }}
                >
                  开始
                </Button>
                <Button
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAssign(order);
                  }}
                >
                  改派
                </Button>
              </>
            )}

            {order.status === OrderStatus.服务中 && (
              <Button
                size="small"
                type="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  onComplete(order, order.employeeId!);
                }}
              >
                完成
              </Button>
            )}

            {order.status === OrderStatus.已评价 && (
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onViewReview(order);
                }}
              >
                查看评价
              </Button>
            )}

            {[OrderStatus.已完成, OrderStatus.已评价].includes(
              order.status as any,
            ) && (
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onViewServicePhotos(order);
                }}
              >
                服务照片
              </Button>
            )}

            {/* 特殊情况说明按钮 - 只有当订单有特殊情况说明时才显示 */}
            {order.hasSpecialNote &&
              [
                OrderStatus.服务中,
                OrderStatus.已完成,
                OrderStatus.已评价,
              ].includes(order.status as any) && (
                <Button
                  size="small"
                  icon={<ExclamationCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewSpecialNotes(order);
                  }}
                >
                  特殊情况
                </Button>
              )}

            {order.status === OrderStatus.退款中 && (
              <Button
                size="small"
                type="primary"
                danger
                onClick={(e) => {
                  e.stopPropagation();
                  onAuditRefund(order);
                }}
              >
                退款审核
              </Button>
            )}
          </Space>
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '16px' }}>
      {/* 第一行：主要状态 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        {[
          OrderStatus.待接单,
          OrderStatus.待服务,
          OrderStatus.已出发,
          OrderStatus.服务中,
        ].map((status) => {
          const config = statusConfig[status];
          const statusOrders = groupedOrders[status] || [];
          return (
            <Col span={6} key={status}>
              <Card
                title={
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <span>{config.title}</span>
                    {[OrderStatus.待接单, OrderStatus.待服务].includes(
                      status,
                    ) ? (
                      <Badge count={statusOrders.length} />
                    ) : !!statusOrders.length ? (
                      <span
                        style={{
                          color: '#999',
                          fontWeight: 'normal',
                          fontSize: '12px',
                        }}
                      >
                        {statusOrders.length}次
                      </span>
                    ) : null}
                  </div>
                }
                style={{ height: '400px', overflow: 'hidden' }}
                styles={{
                  body: { height: '320px', overflowY: 'auto', padding: '12px' },
                }}
              >
                {statusOrders.map(renderOrderCard)}
                {statusOrders.length === 0 && (
                  <div
                    style={{
                      textAlign: 'center',
                      color: '#999',
                      padding: '20px',
                    }}
                  >
                    暂无订单
                  </div>
                )}
              </Card>
            </Col>
          );
        })}
      </Row>

      {/* 第二行：完成和其他状态 */}
      <Row gutter={16}>
        {[
          OrderStatus.已完成,
          OrderStatus.已评价,
          OrderStatus.待付款,
          OrderStatus.已取消,
          OrderStatus.退款中,
          OrderStatus.已退款,
        ].map((status) => {
          const config = statusConfig[status];
          const statusOrders = groupedOrders[status] || [];
          return (
            <Col span={4} key={status}>
              <Card
                title={
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <span style={{ fontSize: '12px' }}>{config.title}</span>
                    {[OrderStatus.退款中].includes(status) ? (
                      <Badge count={statusOrders.length} />
                    ) : !!statusOrders.length ? (
                      <span
                        style={{
                          color: '#999',
                          fontWeight: 'normal',
                          fontSize: '12px',
                        }}
                      >
                        {statusOrders.length}次
                      </span>
                    ) : null}
                  </div>
                }
                style={{ height: '350px', overflow: 'hidden' }}
                styles={{
                  body: {
                    height: '270px',
                    overflowY: 'auto',
                    padding: '8px',
                  },
                }}
              >
                {statusOrders.map(renderOrderCard)}
                {statusOrders.length === 0 && (
                  <div
                    style={{
                      textAlign: 'center',
                      color: '#999',
                      padding: '15px',
                      fontSize: '12px',
                    }}
                  >
                    暂无订单
                  </div>
                )}
              </Card>
            </Col>
          );
        })}
      </Row>
    </div>
  );
};

export default StatusBoardView;
