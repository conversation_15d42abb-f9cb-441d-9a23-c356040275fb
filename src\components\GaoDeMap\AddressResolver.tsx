import React from 'react';
import { Card, Typography, Spin, Space } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import { useReverseGeocode } from './hooks';

const { Text } = Typography;

interface AddressResolverProps {
  longitude?: number | string;
  latitude?: number | string;
  originalAddress?: string;
  showCoordinates?: boolean;
  style?: React.CSSProperties;
}

/**
 * 地址解析组件
 * 将经纬度坐标转换为详细地址
 */
const AddressResolver: React.FC<AddressResolverProps> = ({
  longitude,
  latitude,
  originalAddress,
  showCoordinates = true,
  style,
}) => {
  const { address, loading, error } = useReverseGeocode(longitude, latitude);

  if (!longitude || !latitude) {
    return originalAddress ? (
      <div style={style}>
        <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
        <Text>{originalAddress}</Text>
      </div>
    ) : null;
  }

  return (
    <div style={style}>
      <Space direction="vertical" size={4}>
        {/* 原始地址 */}
        {originalAddress && (
          <div>
            <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            <Text>{originalAddress}</Text>
          </div>
        )}

        {/* 解析后的详细地址 */}
        {loading ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Spin size="small" style={{ marginRight: 4 }} />
            <Text type="secondary">正在解析地址...</Text>
          </div>
        ) : error ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="danger">地址解析失败</Text>
          </div>
        ) : address && address !== originalAddress ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="secondary">详细地址: {address}</Text>
          </div>
        ) : null}

        {/* 经纬度坐标 */}
        {showCoordinates && (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="secondary">
              坐标: ({parseFloat(longitude.toString()).toFixed(6)}, {parseFloat(latitude.toString()).toFixed(6)})
            </Text>
          </div>
        )}
      </Space>
    </div>
  );
};

export default AddressResolver;
