import React from 'react';
import { Typography, Spin, Space } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import { useReverseGeocode } from './hooks';

const { Text } = Typography;

interface AddressResolverProps {
  longitude?: number | string;
  latitude?: number | string;
  originalAddress?: string;
  showCoordinates?: boolean;
  style?: React.CSSProperties;
  compact?: boolean; // 紧凑模式，用于列表显示
}

/**
 * 地址解析组件
 * 将经纬度坐标转换为详细地址
 */
const AddressResolver: React.FC<AddressResolverProps> = ({
  longitude,
  latitude,
  originalAddress,
  showCoordinates = true,
  style,
  compact = false,
}) => {
  const { address, loading, error } = useReverseGeocode(longitude, latitude);

  if (!longitude || !latitude) {
    return originalAddress ? (
      <div style={style}>
        <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
        <Text>{originalAddress}</Text>
      </div>
    ) : null;
  }

  // 紧凑模式：用于列表显示
  if (compact) {
    return (
      <div style={style}>
        {loading ? (
          <div>
            <Spin size="small" style={{ marginRight: 4 }} />
            <Text type="secondary">解析中...</Text>
          </div>
        ) : error ? (
          <div>
            <EnvironmentOutlined style={{ marginRight: 4, color: '#ff4d4f' }} />
            <Text>{originalAddress || '地址解析失败'}</Text>
          </div>
        ) : (
          <div>
            <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            <div>
              {/* 优先显示解析后的地址，如果没有则显示原始地址 */}
              <Text ellipsis style={{ maxWidth: '100%' }}>
                {address || originalAddress || '无位置信息'}
              </Text>
              {/* 如果有原始地址且与解析地址不同，显示原始地址 */}
              {originalAddress && address && address !== originalAddress && (
                <div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    原始: {originalAddress}
                  </Text>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  // 完整模式：用于详情显示
  return (
    <div style={style}>
      <Space direction="vertical" size={4}>
        {/* 原始地址 */}
        {originalAddress && (
          <div>
            <EnvironmentOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            <Text>{originalAddress}</Text>
          </div>
        )}

        {/* 解析后的详细地址 */}
        {loading ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Spin size="small" style={{ marginRight: 4 }} />
            <Text type="secondary">正在解析地址...</Text>
          </div>
        ) : error ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="danger">地址解析失败</Text>
          </div>
        ) : address && address !== originalAddress ? (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="secondary">详细地址: {address}</Text>
          </div>
        ) : null}

        {/* 经纬度坐标 */}
        {showCoordinates && (
          <div style={{ marginLeft: originalAddress ? 20 : 0 }}>
            <Text type="secondary">
              坐标: ({parseFloat(longitude.toString()).toFixed(6)}, {parseFloat(latitude.toString()).toFixed(6)})
            </Text>
          </div>
        )}
      </Space>
    </div>
  );
};

export default AddressResolver;
