import { DictionarieState } from '@/models/dictionarie';
import {
  ModalForm,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Vehicle;
  onSave: (info: API.Vehicle) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        form.resetFields();
        form.setFieldsValue({
          status: '空闲', // 默认状态
        });
      }
    }
  }, [open, info, form]);

  // 获取车辆类型选项
  const vehicleTypeList =
    dictionarie?.list?.filter(
      (item: API.Dictionarie) => item.type === '车辆类型' && item.status === 1,
    ) || [];
  const vehicleTypeOptions = vehicleTypeList.map((item: API.Dictionarie) => ({
    label: item.name,
    value: item.code,
  }));

  return (
    <ModalForm<API.Vehicle>
      title={info ? '编辑车辆信息' : '注册车辆信息'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      width={400}
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      form={form}
      isKeyPressSubmit
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="plateNumber"
        label="车牌号"
        rules={[{ required: true, message: '请输入车牌号！' }]}
      />
      <ProFormSelect
        name="vehicleType"
        label="车辆类型"
        placeholder="请选择车辆类型"
        options={vehicleTypeOptions}
        rules={[{ required: true, message: '请选择车辆类型！' }]}
      />
      <ProFormSegmented
        name="status"
        label="车辆状态"
        valueEnum={{
          空闲: '空闲',
          服务中: '服务中',
        }}
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(EditModal);
