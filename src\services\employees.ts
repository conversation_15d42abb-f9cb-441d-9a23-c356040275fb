import { request } from '@umijs/max';

/** 查询列表  GET /employees */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Employee[] }>>(
    '/employees',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /employees */
export async function create(body: Omit<API.Employee, 'id'>) {
  return request<API.ResType<API.Employee>>('/employees', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /employees/:id */
export async function show(id: number) {
  return request<API.ResType<API.Employee>>(`/employees/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /employees/:id */
export async function update(id: number, body: Partial<API.Employee>) {
  return request<API.ResType<unknown>>(`/employees/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /employees/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/employees/${id}`, {
    method: 'DELETE',
  });
}

/** 获取员工最后打卡时间  GET /employees/:id/last-checkin */
export async function getLastCheckinTime(id: number) {
  return request<API.ResType<{ lastCheckinTime?: string }>>(`/employees/${id}/last-checkin`, {
    method: 'GET',
  });
}
