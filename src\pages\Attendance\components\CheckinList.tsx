import { employeeCheckins, employees } from '@/services';
import {
  DeleteOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  PictureOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Avatar,
  Button,
  Image,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const { Text, Paragraph } = Typography;

interface CheckinListProps {
  onViewDetail: (checkin: API.EmployeeCheckin) => void;
  onRefresh?: () => void;
}

const CheckinList: React.FC<CheckinListProps> = ({
  onViewDetail,
  onRefresh,
}) => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  /** 删除单个记录 */
  const handleDelete = async (id: number) => {
    try {
      const { errCode, msg } = await employeeCheckins.remove(id);
      if (errCode) {
        message.error(msg || '删除失败');
      } else {
        message.success('删除成功');
        actionRef.current?.reload();
        onRefresh?.();
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  /** 批量删除记录 */
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const { errCode, msg } = await employeeCheckins.batchRemove(
        selectedRowKeys as number[],
      );
      if (errCode) {
        message.error(msg || '批量删除失败');
      } else {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        actionRef.current?.reload();
        onRefresh?.();
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  const columns: ProColumns<API.EmployeeCheckin>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
      render: (_, record) => <Tag color="blue">#{record.id}</Tag>,
    },
    {
      title: '员工信息',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 150,
      valueType: 'select',
      request: async () => {
        const { errCode, data } = await employees.index({});
        if (errCode || !data?.list) return [];
        return data.list.map((emp) => ({
          label: `${emp.name} (${emp.phone})`,
          value: emp.id,
        }));
      },
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.employee?.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <Text strong>{record.employee?.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.employee?.phone}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '打卡照片',
      dataIndex: 'photos',
      key: 'photos',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <Space>
          <PictureOutlined />
          <Text>{record.photos?.length || 0}张</Text>
          {record.photos && record.photos.length > 0 && (
            <Image
              src={record.photos[0]}
              alt="打卡照片"
              width={40}
              height={40}
              style={{
                objectFit: 'cover',
                borderRadius: 4,
                border: '1px solid #f0f0f0',
              }}
              preview={{
                src: record.photos[0],
              }}
            />
          )}
        </Space>
      ),
    },
    {
      title: '打卡描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      hideInSearch: true,
      render: (_, record) =>
        record.description ? (
          <Paragraph
            style={{ marginBottom: 0, maxWidth: 180 }}
            ellipsis={{ rows: 2, tooltip: record.description }}
          >
            {record.description}
          </Paragraph>
        ) : (
          <Text type="secondary">无描述</Text>
        ),
    },
    {
      title: '打卡位置',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      hideInSearch: true,
      render: (_, record) =>
        record.address ? (
          <Tooltip title={record.address}>
            <Space>
              <EnvironmentOutlined style={{ color: '#1890ff' }} />
              <Text ellipsis style={{ maxWidth: 150 }}>
                {record.address}
              </Text>
            </Space>
          </Tooltip>
        ) : (
          <Text type="secondary">无位置信息</Text>
        ),
    },
    {
      title: '打卡时间',
      dataIndex: 'checkInTime',
      key: 'checkInTime',
      width: 180,
      valueType: 'dateTimeRange',
      search: {
        transform: (value) => ({
          startDate: value[0],
          endDate: value[1],
        }),
      },
      render: (_, record) => (
        <Text>{dayjs(record.checkInTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      hideInSearch: true,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewDetail(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条打卡记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<API.EmployeeCheckin>
      actionRef={actionRef}
      rowKey="id"
      headerTitle="员工打卡记录"
      columns={columns}
      scroll={{ x: 1200 }}
      rowSelection={{
        selectedRowKeys,
        onChange: setSelectedRowKeys,
      }}
      tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
        <Space size={24}>
          <span>
            已选择 <strong>{selectedRowKeys.length}</strong> 项
            <Button type="link" size="small" onClick={onCleanSelected}>
              取消选择
            </Button>
          </span>
        </Space>
      )}
      tableAlertOptionRender={() => (
        <Space size={16}>
          <Popconfirm
            title="确定要删除选中的打卡记录吗？"
            onConfirm={handleBatchDelete}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger>
              批量删除
            </Button>
          </Popconfirm>
        </Space>
      )}
      request={async (params, sort) => {
        const { errCode, msg, data } = await employeeCheckins.index({
          ...params,
          ...sort,
        });
        if (errCode) {
          message.error(msg || '列表查询失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
        return {
          data: data?.list || [],
          total: data?.total || 0,
          success: true,
        };
      }}
      toolBarRender={() => [
        <Button key="refresh" onClick={() => actionRef.current?.reload()}>
          刷新
        </Button>,
      ]}
    />
  );
};

export default CheckinList;
