import { employeeCheckins, employees } from '@/services';
import {
  DeleteOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  PictureOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Avatar,
  Button,
  Image,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const { Text, Paragraph } = Typography;

interface CheckinListProps {
  onViewDetail: (checkin: API.EmployeeCheckin) => void;
  onRefresh?: () => void;
}

const CheckinList: React.FC<CheckinListProps> = ({
  onViewDetail,
  onRefresh,
}) => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  /** 删除单个记录 */
  const handleDelete = async (id: number) => {
    try {
      const { errCode, msg } = await employeeCheckins.remove(id);
      if (errCode) {
        message.error(msg || '删除失败');
      } else {
        message.success('删除成功');
        actionRef.current?.reload();
        onRefresh?.();
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  /** 批量删除记录 */
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      const { errCode, msg } = await employeeCheckins.batchRemove(
        selectedRowKeys as number[],
      );
      if (errCode) {
        message.error(msg || '批量删除失败');
      } else {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        actionRef.current?.reload();
        onRefresh?.();
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  const columns: ProColumns<API.EmployeeCheckin>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
      render: (_, record) => <Tag color="blue">#{record.id}</Tag>,
    },
    {
      title: '员工信息',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 150,
      valueType: 'select',
      request: async () => {
        const { errCode, data } = await employees.index({});
        if (errCode || !data?.list) return [];
        return data.list.map((emp) => ({
          label: `${emp.name} (${emp.phone})`,
          value: emp.id,
        }));
      },
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.employee?.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <div>
              <Text strong>{record.employee?.name}</Text>
              {record.employee?.level && (
                <Tag color="blue" size="small" style={{ marginLeft: 4 }}>
                  {record.employee.level}级
                </Tag>
              )}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {record.employee?.phone}
            </div>
            {record.employee?.rating && (
              <div style={{ fontSize: 12, color: '#faad14' }}>
                ⭐ {record.employee.rating.toFixed(1)}分
              </div>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: '打卡照片',
      dataIndex: 'photos',
      key: 'photos',
      width: 200,
      hideInSearch: true,
      render: (_, record) => {
        const photos = record.photos || {};
        const allPhotos = [
          ...(photos.vehicleExterior || []),
          ...(photos.serviceStaff || []),
          ...(photos.vehicleInterior || []),
        ];
        const displayPhotos = allPhotos.slice(0, 3); // 最多显示3张
        const remainingCount = allPhotos.length - displayPhotos.length;

        // 计算各分组照片数量
        const vehicleExteriorCount = photos.vehicleExterior?.length || 0;
        const serviceStaffCount = photos.serviceStaff?.length || 0;
        const vehicleInteriorCount = photos.vehicleInterior?.length || 0;

        return (
          <Space direction="vertical" size={4}>
            <Space>
              <PictureOutlined />
              <Text type="secondary">共 {allPhotos.length} 张</Text>
            </Space>
            <Space size={4}>
              {vehicleExteriorCount > 0 && (
                <Tag color="blue" size="small">
                  外观 {vehicleExteriorCount}
                </Tag>
              )}
              {serviceStaffCount > 0 && (
                <Tag color="green" size="small">
                  人员 {serviceStaffCount}
                </Tag>
              )}
              {vehicleInteriorCount > 0 && (
                <Tag color="orange" size="small">
                  车内 {vehicleInteriorCount}
                </Tag>
              )}
            </Space>
            {displayPhotos.length > 0 && (
              <Image.PreviewGroup>
                <Space>
                  {displayPhotos.map((photo, index) => (
                    <Image
                      key={index}
                      src={photo}
                      alt={`打卡照片${index + 1}`}
                      width={40}
                      height={40}
                      style={{
                        objectFit: 'cover',
                        borderRadius: 4,
                        border: '1px solid #f0f0f0',
                      }}
                      placeholder={
                        <div
                          style={{
                            width: 40,
                            height: 40,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f5f5f5',
                            borderRadius: 4,
                            fontSize: 10,
                          }}
                        >
                          加载中
                        </div>
                      }
                    />
                  ))}
                  {remainingCount > 0 && (
                    <div
                      style={{
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: 4,
                        border: '1px solid #f0f0f0',
                        fontSize: 12,
                        color: '#666',
                      }}
                    >
                      +{remainingCount}
                    </div>
                  )}
                </Space>
              </Image.PreviewGroup>
            )}
          </Space>
        );
      },
    },
    {
      title: '打卡描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      hideInSearch: true,
      render: (_, record) =>
        record.description ? (
          <Paragraph
            style={{ marginBottom: 0, maxWidth: 180 }}
            ellipsis={{ rows: 2, tooltip: record.description }}
          >
            {record.description}
          </Paragraph>
        ) : (
          <Text type="secondary">无描述</Text>
        ),
    },
    {
      title: '打卡位置',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      hideInSearch: true,
      render: (_, record) =>
        record.address ? (
          <Tooltip title={record.address}>
            <Space>
              <EnvironmentOutlined style={{ color: '#1890ff' }} />
              <Text ellipsis style={{ maxWidth: 150 }}>
                {record.address}
              </Text>
            </Space>
          </Tooltip>
        ) : (
          <Text type="secondary">无位置信息</Text>
        ),
    },
    {
      title: '打卡时间',
      dataIndex: 'checkInTime',
      key: 'checkInTime',
      width: 180,
      valueType: 'dateTimeRange',
      search: {
        transform: (value) => ({
          startDate: value[0],
          endDate: value[1],
        }),
      },
      render: (_, record) => (
        <Text>{dayjs(record.checkInTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      hideInSearch: true,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewDetail(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条打卡记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<API.EmployeeCheckin>
      actionRef={actionRef}
      rowKey="id"
      headerTitle="员工打卡记录"
      columns={columns}
      scroll={{ x: 1200 }}
      rowSelection={{
        selectedRowKeys,
        onChange: setSelectedRowKeys,
      }}
      tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
        <Space size={24}>
          <span>
            已选择 <strong>{selectedRowKeys.length}</strong> 项
            <Button type="link" size="small" onClick={onCleanSelected}>
              取消选择
            </Button>
          </span>
        </Space>
      )}
      tableAlertOptionRender={() => (
        <Space size={16}>
          <Popconfirm
            title="确定要删除选中的打卡记录吗？"
            onConfirm={handleBatchDelete}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger>
              批量删除
            </Button>
          </Popconfirm>
        </Space>
      )}
      request={async (params, sort) => {
        const { errCode, msg, data } = await employeeCheckins.index({
          ...params,
          ...sort,
        });
        if (errCode) {
          message.error(msg || '列表查询失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
        return {
          data: data?.list || [],
          total: data?.total || 0,
          success: true,
        };
      }}
      toolBarRender={() => [
        <Button key="refresh" onClick={() => actionRef.current?.reload()}>
          刷新
        </Button>,
      ]}
    />
  );
};

export default CheckinList;
