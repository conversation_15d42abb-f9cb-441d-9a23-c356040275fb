import React, { useState } from 'react';
import { Button, Card, Input, Space, Typography, Alert, Spin } from 'antd';
import { reverseGeocode } from './utils';
import { useReverseGeocode } from './hooks';

const { Title, Text, Paragraph } = Typography;

/**
 * 逆地理编码测试组件
 * 用于测试和调试地址解析功能
 */
const ReverseGeocodeTest: React.FC = () => {
  const [longitude, setLongitude] = useState('108.940200');
  const [latitude, setLatitude] = useState('34.267030');
  const [manualResult, setManualResult] = useState<string>('');
  const [manualLoading, setManualLoading] = useState(false);
  const [manualError, setManualError] = useState<string>('');

  // 使用Hook进行自动测试
  const { address: hookAddress, loading: hookLoading, error: hookError } = useReverseGeocode(
    longitude,
    latitude,
  );

  // 手动调用测试
  const handleManualTest = async () => {
    setManualLoading(true);
    setManualError('');
    setManualResult('');

    try {
      console.log('🧪 [测试] 开始手动调用reverseGeocode');
      const result = await reverseGeocode(longitude, latitude);
      console.log('🧪 [测试] 手动调用成功:', result);
      setManualResult(result);
    } catch (error) {
      console.error('🧪 [测试] 手动调用失败:', error);
      setManualError(error instanceof Error ? error.message : '未知错误');
    } finally {
      setManualLoading(false);
    }
  };

  // 测试不同的坐标
  const testCoordinates = [
    { name: '西安市中心', lng: '108.940200', lat: '34.267030' },
    { name: '北京天安门', lng: '116.397428', lat: '39.90923' },
    { name: '上海外滩', lng: '121.499763', lat: '31.245943' },
    { name: '深圳市中心', lng: '114.085947', lat: '22.547' },
  ];

  const handleQuickTest = (lng: string, lat: string) => {
    setLongitude(lng);
    setLatitude(lat);
  };

  return (
    <div style={{ padding: 20 }}>
      <Title level={2}>逆地理编码测试工具</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 坐标输入 */}
        <Card title="坐标输入" size="small">
          <Space>
            <div>
              <Text>经度:</Text>
              <Input
                value={longitude}
                onChange={(e) => setLongitude(e.target.value)}
                placeholder="请输入经度"
                style={{ width: 120, marginLeft: 8 }}
              />
            </div>
            <div>
              <Text>纬度:</Text>
              <Input
                value={latitude}
                onChange={(e) => setLatitude(e.target.value)}
                placeholder="请输入纬度"
                style={{ width: 120, marginLeft: 8 }}
              />
            </div>
            <Button type="primary" onClick={handleManualTest} loading={manualLoading}>
              手动测试
            </Button>
          </Space>
        </Card>

        {/* 快速测试按钮 */}
        <Card title="快速测试" size="small">
          <Space wrap>
            {testCoordinates.map((coord) => (
              <Button
                key={coord.name}
                onClick={() => handleQuickTest(coord.lng, coord.lat)}
                size="small"
              >
                {coord.name}
              </Button>
            ))}
          </Space>
        </Card>

        {/* Hook测试结果 */}
        <Card title="Hook自动测试结果" size="small">
          {hookLoading ? (
            <div>
              <Spin size="small" style={{ marginRight: 8 }} />
              <Text>Hook正在解析地址...</Text>
            </div>
          ) : hookError ? (
            <Alert
              message="Hook测试失败"
              description={hookError}
              type="error"
              showIcon
            />
          ) : hookAddress ? (
            <Alert
              message="Hook测试成功"
              description={hookAddress}
              type="success"
              showIcon
            />
          ) : (
            <Text type="secondary">等待输入坐标...</Text>
          )}
        </Card>

        {/* 手动测试结果 */}
        <Card title="手动测试结果" size="small">
          {manualLoading ? (
            <div>
              <Spin size="small" style={{ marginRight: 8 }} />
              <Text>正在手动解析地址...</Text>
            </div>
          ) : manualError ? (
            <Alert
              message="手动测试失败"
              description={manualError}
              type="error"
              showIcon
            />
          ) : manualResult ? (
            <Alert
              message="手动测试成功"
              description={manualResult}
              type="success"
              showIcon
            />
          ) : (
            <Text type="secondary">点击"手动测试"按钮开始测试</Text>
          )}
        </Card>

        {/* 调试信息 */}
        <Card title="调试信息" size="small">
          <Paragraph>
            <Text strong>当前坐标:</Text> ({longitude}, {latitude})
          </Paragraph>
          <Paragraph>
            <Text strong>高德地图API状态:</Text>{' '}
            {(window as any).AMap ? (
              <Text type="success">✅ 已加载</Text>
            ) : (
              <Text type="danger">❌ 未加载</Text>
            )}
          </Paragraph>
          <Paragraph>
            <Text strong>调试日志:</Text> 请打开浏览器开发者工具查看控制台输出
          </Paragraph>
          <Alert
            message="使用说明"
            description={
              <div>
                <p>1. 输入经纬度坐标或点击快速测试按钮</p>
                <p>2. Hook会自动触发地址解析</p>
                <p>3. 也可以点击"手动测试"按钮进行测试</p>
                <p>4. 查看浏览器控制台获取详细调试信息</p>
                <p>5. 测试坐标: 西安市中心 (108.940200, 34.267030)</p>
              </div>
            }
            type="info"
            showIcon
          />
        </Card>
      </Space>
    </div>
  );
};

export default ReverseGeocodeTest;
