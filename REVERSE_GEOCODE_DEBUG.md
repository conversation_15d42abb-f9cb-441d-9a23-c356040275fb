# 逆地理编码调试指南

## 问题描述
逆地理编码功能失败，需要添加调试日志来定位问题。

## 测试坐标
- **经度**: 108.940200
- **纬度**: 34.267030
- **位置**: 西安市中心

## 调试步骤

### 1. 访问测试页面
在浏览器中访问：`http://localhost:8001/settings/reverseGeocode`

### 2. 打开开发者工具
1. 按 `F12` 打开开发者工具
2. 切换到 `Console` 标签页
3. 清空控制台日志

### 3. 进行测试
1. 在测试页面输入坐标：
   - 经度：`108.940200`
   - 纬度：`34.267030`
2. 点击"手动测试"按钮
3. 观察控制台输出

### 4. 查看调试日志

#### 正常流程日志
```
🌍 [逆地理编码] 开始解析坐标: {longitude: "108.940200", latitude: "34.267030"}
✅ [逆地理编码] 高德地图API已加载
🔍 [逆地理编码] AMap对象信息: {version: "2.0", Geocoder: true, LngLat: true, ...}
📍 [逆地理编码] 解析后的坐标: {lng: 108.9402, lat: 34.26703}
🔧 [逆地理编码] 创建Geocoder实例...
✅ [逆地理编码] Geocoder实例创建成功
📌 [逆地理编码] LngLat对象创建成功: LngLat对象
🚀 [逆地理编码] 开始调用getAddress...
📡 [逆地理编码] getAddress回调触发: {status: "complete", result: {...}}
✅ [逆地理编码] API调用成功
📋 [逆地理编码] regeocode数据: {...}
🏠 [逆地理编码] 解析得到地址: "陕西省西安市雁塔区..."
✅ [逆地理编码] 地址解析成功: "陕西省西安市雁塔区..."
```

#### Hook调用日志
```
🎣 [Hook] fetchAddress被调用: {longitude: "108.940200", latitude: "34.267030"}
🎣 [Hook] 开始获取地址，设置loading=true
🎣 [Hook] 动态导入utils模块...
🎣 [Hook] utils模块导入成功，调用reverseGeocode...
🎣 [Hook] reverseGeocode调用成功: "陕西省西安市雁塔区..."
🎣 [Hook] 地址设置完成
🎣 [Hook] 设置loading=false
```

### 5. 常见问题排查

#### 问题1: 高德地图API未加载
**日志特征**:
```
❌ [逆地理编码] 高德地图API未加载
🔍 [逆地理编码] 检查window对象: []
```

**解决方案**:
1. 检查网络连接
2. 检查API密钥配置
3. 检查控制台是否有API加载错误

#### 问题2: 坐标格式无效
**日志特征**:
```
❌ [逆地理编码] 坐标格式无效: {longitude: "abc", latitude: "def"}
```

**解决方案**:
1. 确保坐标为有效数字
2. 检查坐标范围是否合理

#### 问题3: API调用失败
**日志特征**:
```
❌ [逆地理编码] API调用失败: {status: "error", result: {...}}
```

**解决方案**:
1. 检查API密钥权限
2. 检查网络连接
3. 检查坐标是否在服务范围内

#### 问题4: Geocoder创建失败
**日志特征**:
```
💥 [逆地理编码] 执行过程中发生异常: Error: ...
```

**解决方案**:
1. 检查高德地图API版本
2. 检查Geocoder插件是否正确加载

### 6. 高德地图API配置检查

#### 当前配置
```typescript
AMAP_CONFIG = {
  key: '60b9934af1d9787dfff1725c8494d55e',
  securityJsCode: 'f40b7371e64d91c3c2fb78b9d03531db',
}
```

#### 验证步骤
1. 在控制台输入：`console.log(AMAP_CONFIG)`
2. 检查配置是否正确
3. 在控制台输入：`console.log(window.AMap)`
4. 检查API是否加载

### 7. 网络请求检查

#### 检查步骤
1. 打开开发者工具的 `Network` 标签
2. 刷新页面
3. 查找高德地图相关的请求
4. 检查请求状态和响应

#### 预期请求
- `https://webapi.amap.com/maps?...` - 地图API加载
- `https://restapi.amap.com/v3/geocode/regeo?...` - 逆地理编码请求

### 8. 测试用例

#### 测试用例1: 西安市中心
- 经度: 108.940200
- 纬度: 34.267030
- 预期结果: 陕西省西安市相关地址

#### 测试用例2: 北京天安门
- 经度: 116.397428
- 纬度: 39.90923
- 预期结果: 北京市东城区相关地址

#### 测试用例3: 无效坐标
- 经度: 999
- 纬度: 999
- 预期结果: 错误提示

### 9. 故障排除清单

- [ ] 网络连接正常
- [ ] 高德地图API密钥有效
- [ ] API密钥具有逆地理编码权限
- [ ] 浏览器控制台无JavaScript错误
- [ ] 坐标格式正确
- [ ] 坐标在服务范围内
- [ ] 高德地图API正确加载
- [ ] Geocoder插件可用

### 10. 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的控制台日志
2. 网络请求截图
3. 测试坐标和预期结果
4. 浏览器版本和操作系统信息

## 快速测试命令

在浏览器控制台中执行以下命令进行快速测试：

```javascript
// 检查高德地图API
console.log('AMap:', window.AMap);
console.log('Geocoder:', window.AMap?.Geocoder);

// 手动测试逆地理编码
if (window.AMap && window.AMap.Geocoder) {
  const geocoder = new window.AMap.Geocoder();
  const lnglat = new window.AMap.LngLat(108.940200, 34.267030);
  geocoder.getAddress(lnglat, (status, result) => {
    console.log('手动测试结果:', { status, result });
  });
}
```
