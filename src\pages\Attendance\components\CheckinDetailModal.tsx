import { EnvironmentOutlined, UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Descriptions,
  Image,
  Modal,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Text, Paragraph } = Typography;

interface CheckinDetailModalProps {
  visible: boolean;
  checkin?: API.EmployeeCheckin;
  onClose: () => void;
}

const CheckinDetailModal: React.FC<CheckinDetailModalProps> = ({
  visible,
  checkin,
  onClose,
}) => {
  if (!checkin) return null;

  return (
    <Modal
      title="打卡详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Descriptions column={2} bordered>
        <Descriptions.Item label="员工信息" span={2}>
          <Space>
            <Avatar
              src={checkin.employee?.avatar}
              icon={<UserOutlined />}
              size="small"
            />
            <div>
              <div>
                <Text strong>{checkin.employee?.name}</Text>
                {checkin.employee?.level && (
                  <Tag color="blue" size="small" style={{ marginLeft: 8 }}>
                    {checkin.employee.level}级
                  </Tag>
                )}
              </div>
              <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                {checkin.employee?.phone}
                {checkin.employee?.rating && (
                  <span style={{ marginLeft: 8, color: '#faad14' }}>
                    ⭐ {checkin.employee.rating.toFixed(1)}分
                  </span>
                )}
              </div>
            </div>
          </Space>
        </Descriptions.Item>

        <Descriptions.Item label="打卡时间">
          <Text>
            {dayjs(checkin.checkInTime).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="记录ID">
          <Tag color="blue">#{checkin.id}</Tag>
        </Descriptions.Item>

        {checkin.address && (
          <Descriptions.Item label="打卡位置" span={2}>
            <Space>
              <EnvironmentOutlined style={{ color: '#1890ff' }} />
              <Text>{checkin.address}</Text>
              {checkin.longitude && checkin.latitude && (
                <Text type="secondary">
                  ({checkin.longitude.toFixed(6)}, {checkin.latitude.toFixed(6)}
                  )
                </Text>
              )}
            </Space>
          </Descriptions.Item>
        )}

        {checkin.description && (
          <Descriptions.Item label="打卡描述" span={2}>
            <Paragraph
              style={{ marginBottom: 0 }}
              ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
            >
              {checkin.description}
            </Paragraph>
          </Descriptions.Item>
        )}

        <Descriptions.Item label="打卡照片" span={2}>
          {checkin.photos ? (
            <Space direction="vertical" size={16} style={{ width: '100%' }}>
              {/* 车辆外观照片 */}
              {checkin.photos.vehicleExterior &&
                checkin.photos.vehicleExterior.length > 0 && (
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Tag color="blue">
                        车辆外观照片 ({checkin.photos.vehicleExterior.length}张)
                      </Tag>
                    </div>
                    <Image.PreviewGroup>
                      <Space wrap>
                        {checkin.photos.vehicleExterior.map((photo, index) => (
                          <Image
                            key={`exterior-${index}`}
                            src={photo}
                            alt={`车辆外观照片${index + 1}`}
                            width={100}
                            height={100}
                            style={{
                              objectFit: 'cover',
                              borderRadius: 8,
                              border: '1px solid #f0f0f0',
                            }}
                            placeholder={
                              <div
                                style={{
                                  width: 100,
                                  height: 100,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: 8,
                                }}
                              >
                                加载中...
                              </div>
                            }
                          />
                        ))}
                      </Space>
                    </Image.PreviewGroup>
                  </div>
                )}

              {/* 服务人员照片 */}
              {checkin.photos.serviceStaff &&
                checkin.photos.serviceStaff.length > 0 && (
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Tag color="green">
                        服务人员照片 ({checkin.photos.serviceStaff.length}张)
                      </Tag>
                    </div>
                    <Image.PreviewGroup>
                      <Space wrap>
                        {checkin.photos.serviceStaff.map((photo, index) => (
                          <Image
                            key={`staff-${index}`}
                            src={photo}
                            alt={`服务人员照片${index + 1}`}
                            width={100}
                            height={100}
                            style={{
                              objectFit: 'cover',
                              borderRadius: 8,
                              border: '1px solid #f0f0f0',
                            }}
                            placeholder={
                              <div
                                style={{
                                  width: 100,
                                  height: 100,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: 8,
                                }}
                              >
                                加载中...
                              </div>
                            }
                          />
                        ))}
                      </Space>
                    </Image.PreviewGroup>
                  </div>
                )}

              {/* 车内情况照片 */}
              {checkin.photos.vehicleInterior &&
                checkin.photos.vehicleInterior.length > 0 && (
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Tag color="orange">
                        车内情况照片 ({checkin.photos.vehicleInterior.length}张)
                      </Tag>
                    </div>
                    <Image.PreviewGroup>
                      <Space wrap>
                        {checkin.photos.vehicleInterior.map((photo, index) => (
                          <Image
                            key={`interior-${index}`}
                            src={photo}
                            alt={`车内情况照片${index + 1}`}
                            width={100}
                            height={100}
                            style={{
                              objectFit: 'cover',
                              borderRadius: 8,
                              border: '1px solid #f0f0f0',
                            }}
                            placeholder={
                              <div
                                style={{
                                  width: 100,
                                  height: 100,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: 8,
                                }}
                              >
                                加载中...
                              </div>
                            }
                          />
                        ))}
                      </Space>
                    </Image.PreviewGroup>
                  </div>
                )}

              {/* 如果没有任何照片 */}
              {(!checkin.photos.vehicleExterior ||
                checkin.photos.vehicleExterior.length === 0) &&
                (!checkin.photos.serviceStaff ||
                  checkin.photos.serviceStaff.length === 0) &&
                (!checkin.photos.vehicleInterior ||
                  checkin.photos.vehicleInterior.length === 0) && (
                  <Text type="secondary">无照片</Text>
                )}
            </Space>
          ) : (
            <Text type="secondary">无照片</Text>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="创建时间">
          <Text type="secondary">
            {dayjs(checkin.createdAt).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="更新时间">
          <Text type="secondary">
            {dayjs(checkin.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};

export default CheckinDetailModal;
