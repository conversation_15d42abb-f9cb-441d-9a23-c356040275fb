import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import moment from 'moment';
import React, { useRef } from 'react';

interface UsageRecordListProps {
  customerCouponId: number;
}

/**
 * 代金券使用记录列表组件
 */
const UsageRecordList: React.FC<UsageRecordListProps> = ({
  customerCouponId,
}) => {
  const actionRef = useRef<ActionType>();

  // 表格列定义
  const columns: ProColumns<API.CouponUsageRecord>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '使用时间',
      dataIndex: 'useTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.useTime
          ? moment(record.useTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '订单ID',
      dataIndex: 'orderId',
      width: 100,
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: ['order', 'sn'],
      width: 180,
      search: false,
    },
    {
      title: '订单金额',
      dataIndex: ['order', 'totalFee'],
      width: 120,
      valueType: 'money',
      search: false,
    },
  ];

  return (
    <ProTable<API.CouponUsageRecord>
      actionRef={actionRef}
      rowKey="id"
      columns={columns}
      search={false}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      options={false}
      request={async () => {
        if (!customerCouponId) {
          return {
            data: [],
            success: true,
            total: 0,
          };
        }

        try {
          // 这里应该调用获取使用记录的API
          // 由于API可能还未实现，这里先模拟一些数据
          // 实际使用时应该替换为真实API调用
          // const response = await getCouponUsageRecords(customerCouponId);

          // 模拟数据
          const mockData = [
            {
              id: 1,
              customerCouponId,
              useTime: new Date(),
              orderId: 10001,
              order: {
                id: 10001,
                sn: 'ORD20250510001',
                totalFee: 199,
              },
            },
            {
              id: 2,
              customerCouponId,
              useTime: new Date(Date.now() - 86400000), // 昨天
              orderId: 10002,
              order: {
                id: 10002,
                sn: 'ORD20250509001',
                totalFee: 299,
              },
            },
          ];

          return {
            data: mockData as API.CouponUsageRecord[],
            success: true,
            total: mockData.length,
          };
        } catch (error) {
          console.error('获取使用记录失败', error);
          message.error('获取使用记录失败');
          return {
            data: [],
            success: false,
            total: 0,
          };
        }
      }}
    />
  );
};

export default UsageRecordList;
