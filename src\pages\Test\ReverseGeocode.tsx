import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import ReverseGeocodeTest from '@/components/GaoDeMap/ReverseGeocodeTest';

/**
 * 逆地理编码测试页面
 * 路径: /test/reverse-geocode
 */
const ReverseGeocodePage: React.FC = () => {
  return (
    <PageContainer
      title="逆地理编码测试"
      subTitle="测试经纬度坐标转地址功能"
      breadcrumb={{
        items: [
          { title: '测试工具' },
          { title: '逆地理编码测试' },
        ],
      }}
    >
      <ReverseGeocodeTest />
    </PageContainer>
  );
};

export default ReverseGeocodePage;
