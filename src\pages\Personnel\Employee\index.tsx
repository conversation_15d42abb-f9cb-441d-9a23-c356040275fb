import { create, index, remove, update } from '@/services/employees';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Segmented, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const EmployeeManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Employee | undefined>(undefined);

  const handleSave = async (values: API.Employee) => {
    let response;
    console.log('values', values);
    const { id, ...info } = values;
    if (id) {
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Employee) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Employee, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 80,
      fixed: 'left',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 100,
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 100,
      hideInSearch: true,
      valueType: 'avatar',
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '接单等级',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      hideInSearch: true,
      valueType: 'digit',
      fieldProps: {
        min: 1,
        max: 5,
      },
    },
    {
      title: '工作经验(月)',
      dataIndex: 'workExp',
      key: 'workExp',
      width: 100,
      hideInSearch: true,
      valueType: 'digit',
    },
    {
      title: '服务评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      hideInSearch: true,
      valueType: 'digit',
      fieldProps: {
        min: 0,
        max: 5,
      },
    },
    {
      title: '钱包余额',
      dataIndex: 'walletBalance',
      key: 'walletBalance',
      width: 100,
      hideInSearch: true,
      valueType: 'money',
    },
    {
      title: '所属车辆',
      dataIndex: ['vehicle', 'plateNumber'],
      key: 'vehicleId',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      valueEnum: {
        1: '启用',
        0: '禁用',
      },
      filters: true,
      render: (_, entity) => {
        return (
          <Segmented
            size="small"
            value={entity.status}
            options={[
              {
                label: '启用',
                value: 1,
              },
              {
                label: '禁用',
                value: 0,
              },
            ]}
            onChange={(value) => {
              handleSave({
                ...entity,
                status: value,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Employee>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default EmployeeManagement;
