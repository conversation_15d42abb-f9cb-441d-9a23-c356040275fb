import { MembershipCardOrderStatus } from '@/constant';
import { Descriptions, Drawer, Space, Tag, Typography } from 'antd';
import moment from 'moment';
import React from 'react';

const { Text } = Typography;

interface OrderDetailDrawerProps {
  visible: boolean;
  order?: API.MembershipCardOrder;
  onClose: () => void;
}

/**
 * 订单详情抽屉组件
 */
const OrderDetailDrawer: React.FC<OrderDetailDrawerProps> = ({
  visible,
  order,
  onClose,
}) => {
  // 渲染订单状态标签
  const renderStatusTag = (status?: MembershipCardOrderStatus) => {
    if (!status) return <Tag>未知</Tag>;

    switch (status) {
      case MembershipCardOrderStatus.待付款:
        return <Tag color="warning">待付款</Tag>;
      case MembershipCardOrderStatus.已支付:
        return <Tag color="success">已支付</Tag>;
      case MembershipCardOrderStatus.已取消:
        return <Tag>已取消</Tag>;
      case MembershipCardOrderStatus.已退款:
        return <Tag color="error">已退款</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 渲染卡类型标签
  const renderCardTypeTag = (type?: 'discount' | 'times') => {
    if (!type) return <Tag>未知</Tag>;

    switch (type) {
      case 'discount':
        return <Tag color="processing">折扣卡</Tag>;
      case 'times':
        return <Tag color="success">次卡</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  return (
    <Drawer
      title="权益卡订单详情"
      width={600}
      open={visible}
      onClose={onClose}
      destroyOnClose
    >
      {order ? (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Descriptions title="基本信息" column={1} bordered>
            <Descriptions.Item label="订单ID">{order.id}</Descriptions.Item>
            <Descriptions.Item label="订单编号">{order.sn}</Descriptions.Item>
            <Descriptions.Item label="订单状态">
              {renderStatusTag(order.status)}
            </Descriptions.Item>
            <Descriptions.Item label="订单金额">
              <Text type="danger">¥{order.amount}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {order.createdAt
                ? moment(order.createdAt).format('YYYY-MM-DD HH:mm:ss')
                : '-'}
            </Descriptions.Item>
            {order.payTime && (
              <Descriptions.Item label="支付时间">
                {moment(order.payTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.cancelTime && (
              <Descriptions.Item label="取消时间">
                {moment(order.cancelTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.refundTime && (
              <Descriptions.Item label="退款时间">
                {moment(order.refundTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.prepayId && (
              <Descriptions.Item label="微信支付预支付ID">
                {order.prepayId}
              </Descriptions.Item>
            )}
            {order.remark && (
              <Descriptions.Item label="备注">{order.remark}</Descriptions.Item>
            )}
          </Descriptions>

          <Descriptions title="用户信息" column={1} bordered>
            <Descriptions.Item label="用户ID">
              {order.customerId}
            </Descriptions.Item>
            <Descriptions.Item label="用户昵称">
              {order.customer?.nickname || `用户${order.customerId}`}
            </Descriptions.Item>
            <Descriptions.Item label="手机号">
              {order.customer?.phone || '-'}
            </Descriptions.Item>
          </Descriptions>

          <Descriptions title="权益卡信息" column={1} bordered>
            <Descriptions.Item label="权益卡ID">
              {order.cardTypeId}
            </Descriptions.Item>
            <Descriptions.Item label="权益卡名称">
              {order.cardType?.name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="卡类型">
              {renderCardTypeTag(order.cardType?.type)}
            </Descriptions.Item>
            <Descriptions.Item label="售价">
              ¥{order.cardType?.price || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="有效期">
              {order.cardType?.validDays
                ? `${order.cardType.validDays}天`
                : '无固定期限'}
            </Descriptions.Item>
            {order.cardType?.type === 'discount' && (
              <Descriptions.Item label="折扣率">
                {order.cardType.discountRate
                  ? `${(order.cardType.discountRate * 10).toFixed(1)}折`
                  : '-'}
              </Descriptions.Item>
            )}
            {order.cardType?.type === 'times' && (
              <Descriptions.Item label="可用次数">
                {order.cardType.usageLimit
                  ? `${order.cardType.usageLimit}次`
                  : '不限'}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Space>
      ) : (
        <div>加载中...</div>
      )}
    </Drawer>
  );
};

export default OrderDetailDrawer;
