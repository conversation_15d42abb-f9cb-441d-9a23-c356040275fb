import { index } from '@/services/coupons';
import { create } from '@/services/customer-coupons';
import { getCustomers } from '@/services/customers';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Alert, message, Space, Typography } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

const { Text } = Typography;

interface IssueCouponModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  // 用户ID，如果是从用户视图发放，则传入用户ID
  customerId?: number;
  // 代金券ID，如果是从代金券视图发放，则传入代金券ID
  couponId?: number;
}

/**
 * 发放代金券表单组件
 */
const IssueCouponModal: React.FC<IssueCouponModalProps> = ({
  open,
  onClose,
  onSuccess,
  customerId,
  couponId,
}) => {
  // 表单实例引用
  const formRef = useRef<ProFormInstance>();
  // 代金券列表
  const [coupons, setCoupons] = useState<API.Coupon[]>([]);
  // 当前选中的代金券
  const [selectedCoupon, setSelectedCoupon] = useState<API.Coupon | null>(null);
  // 用户查询结果
  const [searchedUser, setSearchedUser] = useState<API.Customer | null>(null);

  // 获取代金券列表
  const fetchCoupons = async () => {
    try {
      const response = await index({ isEnabled: true });
      if (response.errCode) {
        message.error(response.msg || '获取代金券列表失败');
        return;
      }
      setCoupons(response.data?.list || []);

      // 如果传入了couponId，则设置为当前选中的代金券
      if (couponId) {
        const coupon = (response.data?.list || []).find(
          (item) => item.id === couponId,
        );
        if (coupon) {
          setSelectedCoupon(coupon);
        }
      }
    } catch (error) {
      console.error('获取代金券列表失败', error);
      message.error('获取代金券列表失败');
    }
  };

  // 处理用户手机号查询
  const handleUserSearch = async (phone: string) => {
    if (!phone || phone.length !== 11) {
      setSearchedUser(null);
      return;
    }

    try {
      const response = await getCustomers({ phone });
      if (response.errCode) {
        message.error(response.msg || '查询用户失败');
        setSearchedUser(null);
      } else {
        const users = response.data?.list || [];
        if (users.length > 0) {
          const user = users[0];
          setSearchedUser(user);
          // 设置用户ID到表单
          if (formRef.current) {
            formRef.current.setFieldsValue({
              customerId: user.id,
            });
          }
        } else {
          setSearchedUser(null);
          message.warning('未找到该手机号对应的用户');
        }
      }
    } catch (error) {
      console.error('查询用户失败', error);
      message.error('查询用户失败，请重试');
      setSearchedUser(null);
    }
  };

  // 处理代金券选择变化
  const handleCouponChange = (value: number) => {
    const coupon = coupons.find((item) => item.id === value);
    setSelectedCoupon(coupon || null);

    // 如果选择了有有效期的代金券，自动计算到期时间
    if (coupon?.validDays) {
      const calculatedExpiryTime = moment().add(coupon.validDays, 'days');
      // 使用表单实例设置到期时间字段的值
      if (formRef.current) {
        formRef.current.setFieldsValue({
          expiryTime: calculatedExpiryTime,
        });
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!selectedCoupon) {
      message.error('请选择代金券');
      return false;
    }

    if (!customerId && !values.customerId) {
      message.error('请通过手机号查询并选择用户');
      return false;
    }

    // 如果是通过手机号查询的用户，确保已经找到用户
    if (!customerId && !searchedUser) {
      message.error('请先通过手机号查询到有效用户');
      return false;
    }

    try {
      // 计算到期时间
      let expiryTime;
      if (selectedCoupon.validDays) {
        // 如果代金券有有效期，则根据代金券的有效期计算
        expiryTime = moment().add(selectedCoupon.validDays, 'days').toDate();
      } else if (values.expiryTime) {
        // 否则使用手动设置的到期时间
        expiryTime = values.expiryTime;
      }

      // 构建请求参数
      const params: Omit<API.CustomerCoupon, 'id'> = {
        customerId: customerId || values.customerId,
        couponId: values.couponId,
        receiveTime: new Date(),
        expiryTime,
        remainTimes:
          values.remainTimes !== undefined
            ? values.remainTimes
            : selectedCoupon.usageLimit || -1, // 如果设置了次数则使用设置的次数，否则使用代金券类型的次数，如果都没有则为-1（不限次数）
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 调用API发放代金券
      const response = await create(params);
      if (response.errCode) {
        message.error(response.msg || '发放代金券失败');
        return false;
      }

      message.success('发放代金券成功');
      onSuccess();
      return true;
    } catch (error) {
      console.error('发放代金券失败', error);
      message.error('发放代金券失败，请重试');
      return false;
    }
  };

  // 加载代金券列表
  useEffect(() => {
    if (open) {
      fetchCoupons();
    }
  }, [open]);

  return (
    <ModalForm
      title="发放代金券"
      formRef={formRef}
      open={open}
      onFinish={handleSubmit}
      modalProps={{
        onCancel: onClose,
        destroyOnClose: true,
        width: 500,
      }}
      initialValues={{
        couponId: couponId,
        customerId: customerId,
      }}
    >
      {/* 如果没有传入用户ID，则需要通过手机号查询用户 */}
      {!customerId && (
        <>
          <ProFormText
            name="userPhone"
            label="用户手机号"
            placeholder="请输入用户手机号"
            tooltip="请输入要发放代金券的用户手机号"
            rules={[
              { required: true, message: '请输入用户手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]}
            fieldProps={{
              onChange: (e) => {
                // 当手机号输入11位时自动查询
                const phone = e.target.value;
                if (phone && phone.length === 11) {
                  handleUserSearch(phone);
                } else {
                  setSearchedUser(null);
                }
              },
            }}
          />

          <ProFormDependency name={['userPhone']}>
            {({ userPhone }) => {
              if (userPhone && userPhone.length === 11 && searchedUser) {
                return (
                  <Alert
                    message={
                      <Space>
                        <Text>已找到用户：</Text>
                        <Text strong>
                          {searchedUser.nickname || '未设置昵称'}
                        </Text>
                        <Text type="secondary">({searchedUser.phone})</Text>
                      </Space>
                    }
                    type="success"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                );
              }
              return null;
            }}
          </ProFormDependency>

          {/* 隐藏的用户ID字段，用于提交表单 */}
          <ProFormDigit
            name="customerId"
            hidden
            rules={[{ required: true, message: '请先通过手机号查询用户' }]}
          />
        </>
      )}

      {/* 如果没有传入代金券ID，则需要选择代金券 */}
      <ProFormSelect
        name="couponId"
        label="代金券"
        rules={[{ required: true, message: '请选择代金券' }]}
        options={coupons.map((item) => ({
          label: `${item.amount}元代金券（满${item.threshold}元可用）`,
          value: item.id,
        }))}
        fieldProps={{
          onChange: handleCouponChange,
        }}
        disabled={!!couponId}
      />

      {/* 显示代金券的详细信息 */}
      {selectedCoupon && (
        <div style={{ marginBottom: 24 }}>
          <p>面值: ¥{selectedCoupon.amount}</p>
          <p>使用门槛: ¥{selectedCoupon.threshold}</p>
          <p>有效期: {selectedCoupon.validDays || '无限制'}天</p>
          <p>可用次数: {selectedCoupon.usageLimit || '不限'}次</p>
          {selectedCoupon.description && (
            <p>使用说明: {selectedCoupon.description}</p>
          )}
        </div>
      )}

      {/* 到期时间设置 */}
      <ProFormDatePicker
        name="expiryTime"
        label="到期时间"
        placeholder={
          selectedCoupon?.validDays
            ? '已根据代金券有效期自动计算'
            : '请选择到期时间'
        }
        tooltip={
          selectedCoupon?.validDays
            ? '代金券有有效期，到期时间已自动计算'
            : '请选择到期时间，不设置则表示无期限'
        }
        disabled={!!selectedCoupon?.validDays}
      />

      {/* 可用次数设置 */}
      <ProFormDigit
        name="remainTimes"
        label="可用次数"
        placeholder={
          selectedCoupon?.usageLimit
            ? `默认${selectedCoupon.usageLimit}次`
            : '不限次数'
        }
        tooltip="不填写则使用代金券默认次数，填写-1表示不限次数"
        min={-1}
        fieldProps={{ precision: 0 }}
      />
    </ModalForm>
  );
};

export default IssueCouponModal;
