import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/vehicles';
import {
  ModalForm,
  ProFormDigit,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Col, message, Row } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Employee;
  onSave: (info: API.Employee) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  // 获取车辆类型选项
  const employeeTypeList =
    dictionarie?.list?.filter(
      (item: API.Dictionarie) => item.type === '员工职位' && item.status === 1,
    ) || [];
  const employeeTypeOptions = employeeTypeList.map((item: API.Dictionarie) => ({
    label: item.name,
    value: item.code,
  }));

  return (
    <ModalForm<API.Employee>
      title={info ? '编辑员工信息' : '注册员工信息'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      width={600}
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info || { status: '1' }}
    >
      <ProFormText name="id" label="员工ID" hidden />
      <Row style={{ width: '100%' }}>
        <Col span={14}>
          <ProFormText
            name="name"
            label="真实姓名"
            rules={[{ required: true, message: '请输入真实姓名！' }]}
          />
          <ProFormText
            name="phone"
            label="手机号"
            rules={[
              { required: true, message: '请输入手机号！' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号！' },
            ]}
          />
        </Col>
        <Col span={10}>
          <ProFormImg
            name="avatar"
            label="头像"
            maxSize={{
              size: 2 * 1024 * 1024,
              message: '图片大小不能超过1M',
            }}
          />
        </Col>
      </Row>
      <ProFormSelect
        name="position"
        label="职位"
        placeholder="请选择车辆类型"
        options={employeeTypeOptions}
        colProps={{ span: 12 }}
        rules={[{ required: true, message: '请选择职位！' }]}
      />
      <ProFormDigit
        name="level"
        label="接单等级"
        min={1}
        max={5}
        fieldProps={{ precision: 0 }}
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="workExp"
        label="工作经验"
        min={0}
        fieldProps={{ precision: 0, addonAfter: '月' }}
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="rating"
        label="服务评分"
        min={0}
        max={5}
        fieldProps={{ precision: 1 }}
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="walletBalance"
        label="钱包余额"
        fieldProps={{ precision: 2 }}
        colProps={{ span: 12 }}
      />
      <ProFormSelect
        name="vehicleId"
        label="所属车辆"
        request={async () => {
          const { errCode, msg, data } = await index({});
          if (errCode) {
            message.error(msg || '车辆查询失败');
            return [];
          }
          return (data?.list || []).map((item) => ({
            label: item.plateNumber,
            value: item.id,
          }));
        }}
        colProps={{ span: 12 }}
      />
      <ProFormSegmented
        name="status"
        label="启用状态"
        fieldProps={{
          options: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' },
          ],
        }}
        convertValue={(value) => {
          return String(value);
        }}
        colProps={{ span: 12 }}
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(EditModal);
