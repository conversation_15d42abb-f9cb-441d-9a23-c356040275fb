/**
 * 高德地图工具函数
 */

/**
 * 将经纬度坐标转换为像素坐标
 * @param map 地图实例
 * @param position 经纬度坐标 [经度, 纬度]
 * @returns 像素坐标 {x, y}
 */
export const lngLatToPixel = (map: any, position: [number, number]) => {
  if (!map) return { x: 0, y: 0 };
  const lngLat = new (window as any).AMap.LngLat(position[0], position[1]);
  return map.lngLatToContainer(lngLat);
};

/**
 * 将像素坐标转换为经纬度坐标
 * @param map 地图实例
 * @param pixel 像素坐标 {x, y}
 * @returns 经纬度坐标 [经度, 纬度]
 */
export const pixelToLngLat = (map: any, pixel: { x: number; y: number }) => {
  if (!map) return [0, 0] as [number, number];
  const lngLat = map.containerToLngLat(
    new (window as any).AMap.Pixel(pixel.x, pixel.y),
  );
  return [lngLat.getLng(), lngLat.getLat()] as [number, number];
};

/**
 * 计算两点之间的距离（米）
 * @param position1 起点坐标 [经度, 纬度]
 * @param position2 终点坐标 [经度, 纬度]
 * @returns 距离（米）
 */
export const calculateDistance = (
  position1: [number, number],
  position2: [number, number],
) => {
  const AMap = (window as any).AMap;
  if (!AMap) return 0;

  const lnglat1 = new AMap.LngLat(position1[0], position1[1]);
  const lnglat2 = new AMap.LngLat(position2[0], position2[1]);
  return lnglat1.distance(lnglat2);
};

/**
 * 根据标记点状态获取图标URL
 * @param status 标记点状态
 * @returns 图标URL
 */
export const getMarkerIconByStatus = (status?: number) => {
  // 根据不同状态返回不同的图标URL
  switch (status) {
    case 1: // 正常
      return 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png';
    case 2: // 警告
      return 'https://webapi.amap.com/theme/v1.3/markers/n/mark_y.png';
    case 3: // 错误
      return 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png';
    default: // 默认
      return 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png';
  }
};

/**
 * 创建自定义图标样式
 * @param color 图标颜色
 * @param size 图标大小
 * @returns HTML字符串
 */
export const createCustomMarkerStyle = (
  color: string = '#3498db',
  size: number = 12,
) => {
  return `<div style="background-color: ${color}; width: ${size}px; height: ${size}px; border: 2px solid white; border-radius: 50%; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>`;
};

/**
 * 创建聚合点样式
 * @param count 聚合点数量
 * @param color 聚合点颜色
 * @returns HTML字符串
 */
export const createClusterStyle = (
  count: number,
  color: string = 'rgba(0, 161, 214, 0.8)',
) => {
  return `<div style="background-color: ${color}; color: white; width: 36px; height: 36px; line-height: 36px; text-align: center; border-radius: 50%">${count}</div>`;
};

/**
 * 获取地图视野范围内的标记点
 * @param map 地图实例
 * @param markers 所有标记点
 * @returns 视野范围内的标记点
 */
export const getMarkersInView = (map: any, markers: Marker[]) => {
  if (!map) return [];

  const bounds = map.getBounds();
  return markers.filter((marker) => {
    const position = marker.position;
    const lnglat = new (window as any).AMap.LngLat(position[0], position[1]);
    return bounds.contains(lnglat);
  });
};

/**
 * 逆地理编码：将经纬度坐标转换为地址
 * @param longitude 经度
 * @param latitude 纬度
 * @returns Promise<string> 地址字符串
 */
export const reverseGeocode = (
  longitude: number | string,
  latitude: number | string,
): Promise<string> => {
  return new Promise((resolve, reject) => {
    console.log('🌍 [逆地理编码] 开始解析坐标:', { longitude, latitude });

    const AMap = (window as any).AMap;
    if (!AMap) {
      console.error('❌ [逆地理编码] 高德地图API未加载');
      console.log('🔍 [逆地理编码] 检查window对象:', Object.keys(window).filter(key => key.includes('AMap') || key.includes('amap')));
      reject(new Error('高德地图API未加载'));
      return;
    }

    console.log('✅ [逆地理编码] 高德地图API已加载');
    console.log('🔍 [逆地理编码] AMap对象信息:', {
      version: AMap.version,
      Geocoder: !!AMap.Geocoder,
      LngLat: !!AMap.LngLat,
      availablePlugins: Object.keys(AMap).filter(key => key.includes('Geocoder') || key.includes('LngLat'))
    });

    try {
      // 验证坐标有效性
      const lng = parseFloat(longitude.toString());
      const lat = parseFloat(latitude.toString());

      console.log('📍 [逆地理编码] 解析后的坐标:', { lng, lat });

      if (isNaN(lng) || isNaN(lat)) {
        console.error('❌ [逆地理编码] 坐标格式无效:', { longitude, latitude });
        reject(new Error('坐标格式无效'));
        return;
      }

      // 检查坐标范围（中国境内大致范围）
      if (lng < 73 || lng > 135 || lat < 3 || lat > 54) {
        console.warn('⚠️ [逆地理编码] 坐标可能超出中国境内范围:', { lng, lat });
      }

      // 创建逆地理编码实例
      console.log('🔧 [逆地理编码] 创建Geocoder实例...');
      const geocoder = new AMap.Geocoder({
        radius: 1000, // 范围，默认：500
        extensions: 'all', // 返回地址描述以及附近兴趣点和道路信息
      });

      console.log('✅ [逆地理编码] Geocoder实例创建成功');

      const lnglat = new AMap.LngLat(lng, lat);
      console.log('📌 [逆地理编码] LngLat对象创建成功:', lnglat);

      console.log('🚀 [逆地理编码] 开始调用getAddress...');
      geocoder.getAddress(lnglat, (status: string, result: any) => {
        console.log('📡 [逆地理编码] getAddress回调触发:', { status, result });

        if (status === 'complete') {
          console.log('✅ [逆地理编码] API调用成功');

          if (result && result.regeocode) {
            console.log('📋 [逆地理编码] regeocode数据:', result.regeocode);

            const address = result.regeocode.formattedAddress;
            console.log('🏠 [逆地理编码] 解析得到地址:', address);

            if (address) {
              console.log('✅ [逆地理编码] 地址解析成功:', address);
              resolve(address);
            } else {
              console.warn('⚠️ [逆地理编码] formattedAddress为空');
              resolve('未知地址');
            }
          } else {
            console.error('❌ [逆地理编码] result.regeocode不存在:', result);
            reject(new Error('地址解析结果格式异常'));
          }
        } else {
          console.error('❌ [逆地理编码] API调用失败:', { status, result });
          reject(new Error(`地址解析失败: ${status}`));
        }
      });

    } catch (error) {
      console.error('💥 [逆地理编码] 执行过程中发生异常:', error);
      reject(error);
    }
  });
};

/**
 * 批量逆地理编码
 * @param coordinates 坐标数组 [{longitude, latitude}]
 * @returns Promise<string[]> 地址数组
 */
export const batchReverseGeocode = async (
  coordinates: Array<{ longitude: number | string; latitude: number | string }>,
): Promise<string[]> => {
  const promises = coordinates.map((coord) =>
    reverseGeocode(coord.longitude, coord.latitude).catch(() => '地址解析失败'),
  );
  return Promise.all(promises);
};
