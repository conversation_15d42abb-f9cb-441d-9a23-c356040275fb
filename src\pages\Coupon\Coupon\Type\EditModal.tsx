import ScopeSelector from '@/components/ScopeSelector';
import { CurrentApplicableScope } from '@/components/ScopeSelector/types';
import { extractApplicableScopeInfo } from '@/components/ScopeSelector/utils';
import { ApplicableScope } from '@/constant';
import {
  ModalForm,
  ProFormDigit,
  ProFormSegmented,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Coupon;
  onSave: (info: API.Coupon) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 状态
  const [selectedScopes, setSelectedScopes] = useState<
    CurrentApplicableScope[]
  >([]);
  const [scopeSelectMode, setScopeSelectMode] = useState<'all' | 'custom'>(
    'all',
  );

  // 组件初始化时设置初始状态
  useEffect(() => {
    if (!open) return; // 当弹窗关闭时不执行初始化

    // 初始化状态
    if (info) {
      // 编辑模式
      console.log('编辑模式，初始化数据:', info);

      // 处理适用范围
      let scopes: CurrentApplicableScope[] = [];
      const { applicableScope } = info;

      // 根据适用范围类型设置选择模式和选中项
      if (applicableScope === ApplicableScope.不限) {
        // 全部商品和服务
        setScopeSelectMode('all');
        scopes = [{ type: ApplicableScope.不限 }];
      } else {
        // 自定义范围
        setScopeSelectMode('custom');

        // 根据适用范围类型设置选中项
        switch (applicableScope) {
          case ApplicableScope.所有商品:
            scopes = [{ type: ApplicableScope.所有商品 }];
            break;
          case ApplicableScope.所有服务:
            scopes = [{ type: ApplicableScope.所有服务 }];
            break;
          case ApplicableScope.指定服务类别:
            scopes = (info.applicableServiceTypes || []).map((code) => ({
              type: ApplicableScope.指定服务类别,
              code,
            }));
            break;
          case ApplicableScope.指定服务品牌:
            scopes = (info.applicableServiceCategories || []).map((id) => ({
              type: ApplicableScope.指定服务品牌,
              id,
            }));
            break;
          case ApplicableScope.指定服务:
            scopes = (info.applicableServices || []).map((id) => ({
              type: ApplicableScope.指定服务,
              id,
            }));
            break;
          case ApplicableScope.指定商品类别:
            scopes = (info.applicableProductTypes || []).map((id) => ({
              type: ApplicableScope.指定商品类别,
              code: String(id),
            }));
            break;
          case ApplicableScope.指定商品:
            scopes = (info.applicableProducts || []).map((id) => ({
              type: ApplicableScope.指定商品,
              id,
            }));
            break;
          default:
            break;
        }
      }

      setSelectedScopes(scopes);
    } else {
      // 新增模式
      console.log('新增模式，设置初始状态');
      setScopeSelectMode('all');
      setSelectedScopes([{ type: ApplicableScope.不限 }]);
    }
  }, [info, open]);

  // 处理适用范围选择模式变更
  const handleScopeSelectModeChange = (mode: 'all' | 'custom') => {
    setScopeSelectMode(mode);
    if (mode === 'all') {
      setSelectedScopes([{ type: ApplicableScope.不限 }]);
    } else if (
      selectedScopes.length === 0 ||
      selectedScopes[0].type === ApplicableScope.不限
    ) {
      // 如果之前是"全部"模式，切换到自定义模式时，清空选择
      setSelectedScopes([]);
    }
  };

  // 表单提交
  const handleFinish = async (values: any) => {
    // 提取适用范围信息
    const scopeInfo = extractApplicableScopeInfo(
      scopeSelectMode,
      selectedScopes,
    );

    // 构建提交的数据
    const formValues = {
      ...values,
      ...scopeInfo,
    };

    // 移除表单中的scopeSelectMode字段，因为它只是UI控制用的
    delete formValues.scopeSelectMode;

    console.log('提交的数据:', formValues);
    await onSave(formValues);
  };

  // 表单初始值
  const initialValues = info
    ? {
        ...info,
        scopeSelectMode:
          info.applicableScope === ApplicableScope.不限 ? 'all' : 'custom',
      }
    : {
        threshold: 0,
        status: '1',
        scopeSelectMode: 'all',
      };

  console.log('渲染模态框，info:', info, '初始值:', initialValues);

  return (
    <ModalForm<API.Coupon>
      title={info ? '编辑代金券' : '新增代金券'}
      form={form}
      grid
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        width: 800,
      }}
      open={open}
      layout="horizontal"
      labelCol={{ flex: '7em' }}
      onFinish={handleFinish}
      initialValues={initialValues}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormDigit
        name="price"
        label="售价"
        min={0}
        fieldProps={{ precision: 2 }}
        rules={[{ required: true, message: '请输入售价！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="amount"
        label="面额"
        min={0}
        fieldProps={{ precision: 2 }}
        rules={[{ required: true, message: '请输入面额！' }]}
      />
      <ProFormDigit
        name="threshold"
        label="最低消费"
        tooltip="0表示无门槛"
        min={0}
        fieldProps={{ precision: 2 }}
        rules={[{ required: true, message: '请输入最低消费金额！' }]}
      />
      <ProFormDigit
        name="validDays"
        label="有效期(天)"
        tooltip="0或不填表示永久有效"
        min={1}
        fieldProps={{ precision: 0 }}
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="usageLimit"
        label="可用次数"
        tooltip="0或不填表示不限制次数"
        min={0}
        fieldProps={{ precision: 0 }}
        colProps={{ span: 12 }}
      />
      {/* 适用范围选择 */}
      <ScopeSelector
        scopeSelectMode={scopeSelectMode}
        selectedScopes={selectedScopes}
        onScopeSelectModeChange={handleScopeSelectModeChange}
        onScopesChange={setSelectedScopes}
      />

      <ProFormTextArea
        name="description"
        label="使用说明"
        placeholder="请输入代金券的使用说明和限制条件"
        rules={[{ required: true, message: '请输入使用说明！' }]}
      />
      <ProFormSegmented
        name="status"
        label="状态"
        fieldProps={{
          options: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' },
          ],
        }}
        convertValue={(value) => {
          return String(value);
        }}
      />
    </ModalForm>
  );
};

export default EditModal;
